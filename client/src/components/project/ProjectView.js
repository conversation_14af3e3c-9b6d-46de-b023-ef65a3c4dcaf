import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Menu,
  IconButton,
  Chip,
  Collapse,
  Divider,
  CircularProgress,
  FormControlLabel,
  Checkbox,
  FormControl,
  InputLabel,
  Select,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Assignment as AssignmentIcon,
  Folder as FolderIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ChevronRight as ChevronRightIcon,
  History as HistoryIcon,
  Chat as ChatIcon,
  DragIndicator as DragIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import Breadcrumbs from '../common/Breadcrumbs';
import EnhancedAvatar from '../common/EnhancedAvatar';
import { useAuth } from '../../contexts/AuthContext';
import UserBubble from '../common/UserBubble';
import AddUserDialog from '../common/AddUserDialog';
import RichTextDisplay from '../common/RichTextDisplay';
import RichTextEditor from '../common/RichTextEditor';
import RichTextDisplayWithLinking from '../common/RichTextDisplayWithLinking';
import { stateColors } from '../requirement/RequirementView.styles';

const ProjectView = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const { axios, user } = useAuth();
  const [project, setProject] = useState(null);
  const [features, setFeatures] = useState([]);
  const [requirements, setRequirements] = useState([]);
  const [expandedFeatures, setExpandedFeatures] = useState({});
  const [newRequirementDialogOpen, setNewRequirementDialogOpen] = useState(false);
  const [newProjectDialogOpen, setNewProjectDialogOpen] = useState(false);
  const [newProject, setNewProject] = useState({
    name: '',
    description: '',
    addSelf: true
  });
  const [newRequirement, setNewRequirement] = useState({
    type: 'requirement',
    title: '',
    description: '',
    parent: null
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const lastRequestRef = useRef(null);
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [newElement, setNewElement] = useState({
    type: 'feature',
    title: '',
    description: ''
  });
  const [newElementDialogOpen, setNewElementDialogOpen] = useState(false);

  // Comment and linking functionality
  const [comment, setComment] = useState('');
  const [linkedTexts, setLinkedTexts] = useState([]);
  const [hoveredLinkId, setHoveredLinkId] = useState(null);
  const [scrollingToComment, setScrollingToComment] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [editedDescription, setEditedDescription] = useState('');

  // Split view state
  const [commentsWidth, setCommentsWidth] = useState(40);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);
  const commentsContainerRef = useRef(null);

  // Menu state
  const [anchorEl, setAnchorEl] = useState(null);

  useEffect(() => {
    const fetchProjectData = async () => {
      const requestKey = `${projectId}-${Date.now()}`;
      lastRequestRef.current = requestKey;

      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          navigate('/login');
          return;
        }

        const headers = {
          'Authorization': `Bearer ${token}`
        };

        // Fetch project, features, and requirements in parallel
        const [projectRes, featuresRes, requirementsRes] = await Promise.all([
          axios.get(`/api/projects/${projectId}`, { headers }),
          axios.get(`/api/features/project/${projectId}`, { headers }),
          axios.get(`/api/requirements/project/${projectId}`, { headers })
        ]);

        // Only update state if this is still the most recent request
        if (lastRequestRef.current === requestKey) {
          if (!projectRes.data) {
            throw new Error('Project not found');
          }

          setProject(projectRes.data);
          setFeatures(featuresRes.data || []);
          setRequirements(requirementsRes.data || []);

          // Initialize expanded state for features with children
          const initialExpanded = {};
          featuresRes.data.forEach(feature => {
            const hasChildren = requirementsRes.data.some(req => req.feature === feature._id);
            initialExpanded[feature._id] = hasChildren;
          });
          setExpandedFeatures(initialExpanded);
        }
      } catch (error) {
        console.error('Error in fetchProjectData:', error);
        if (lastRequestRef.current === requestKey) {
          if (error.response?.status === 401) {
            navigate('/login');
          } else {
            setError(error.response?.data?.message || error.message || 'Failed to load project data');
          }
        }
      } finally {
        if (lastRequestRef.current === requestKey) {
          setLoading(false);
        }
      }
    };

    if (projectId && projectId !== 'new') {
      fetchProjectData();
    } else if (projectId === 'new') {
      setNewProjectDialogOpen(true);
      setLoading(false);
    }

    return () => {
      lastRequestRef.current = null;
    };
  }, [projectId, navigate, axios]);

  // Initialize linked texts when project loads
  useEffect(() => {
    if (project?.linkedTexts) {
      setLinkedTexts(project.linkedTexts);
    }
  }, [project]);

  // Comment and linking functionality
  const scrollToComment = useCallback((commentId) => {
    if (!commentsContainerRef.current) return;

    const container = commentsContainerRef.current;
    const commentElement = container.querySelector(`[data-comment-id="${commentId}"]`);

    if (commentElement) {
      // Check if comment is visible within the container
      const containerRect = container.getBoundingClientRect();
      const commentRect = commentElement.getBoundingClientRect();

      const isVisible = (
        commentRect.top >= containerRect.top &&
        commentRect.bottom <= containerRect.bottom
      );

      if (!isVisible) {
        setScrollingToComment(commentId);

        // Calculate the scroll position to center the comment in the container
        const containerScrollTop = container.scrollTop;
        const containerHeight = container.clientHeight;
        const commentOffsetTop = commentElement.offsetTop;
        const commentHeight = commentElement.offsetHeight;

        // Calculate target scroll position to center the comment
        const targetScrollTop = commentOffsetTop - (containerHeight / 2) + (commentHeight / 2);

        // Smooth scroll within the container only
        container.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth'
        });

        // Add a temporary highlight effect
        commentElement.style.transition = 'all 0.5s ease';
        commentElement.style.backgroundColor = 'rgba(25, 118, 210, 0.15)';
        commentElement.style.transform = 'scale(1.02)';
        commentElement.style.boxShadow = '0 4px 20px rgba(25, 118, 210, 0.3)';

        setTimeout(() => {
          commentElement.style.backgroundColor = '';
          commentElement.style.transform = '';
          commentElement.style.boxShadow = '';
          setScrollingToComment(null);
        }, 1500);
      }
    }
  }, []);

  const handleLinkHover = useCallback((linkId, isHovering) => {
    setHoveredLinkId(isHovering ? linkId : null);

    if (linkId && isHovering) {
      // Find the linked comment and scroll to it
      const link = linkedTexts.find(l => l.id === linkId);
      if (link) {
        // Small delay to allow hover effects to start
        setTimeout(() => {
          scrollToComment(link.commentId);
        }, 100);
      }
    }
  }, [linkedTexts, scrollToComment]);

  const handleCreateLink = useCallback(async (newLink) => {
    try {
      // Save linked text to database
      await axios.post(`/api/projects/${projectId}/linked-texts`, newLink);
      setLinkedTexts(prev => [...prev, newLink]);
    } catch (error) {
      console.error('Error creating link:', error);
    }
  }, [projectId, axios]);

  const handleCreateNewComment = useCallback(async (selectedText, selectionRange, commentText) => {
    if (!selectedText || !selectionRange || !commentText.trim()) return;

    try {
      // Create the comment first
      const commentResponse = await axios.post(`/api/projects/${projectId}/comments`, {
        text: commentText
      });

      const newComment = commentResponse.data;

      // Create the linked text
      const linkId = `link_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const newLink = {
        id: linkId,
        text: selectedText,
        startOffset: selectionRange.startOffset,
        endOffset: selectionRange.endOffset,
        commentId: newComment._id
      };

      await handleCreateLink(newLink);

      // Update project state with new comment
      setProject(prev => ({
        ...prev,
        comments: [...(prev.comments || []), newComment]
      }));

      // Scroll to the new comment
      setTimeout(() => {
        scrollToComment(newComment._id);
      }, 100);

    } catch (error) {
      console.error('Error creating comment and link:', error);
    }
  }, [projectId, axios, handleCreateLink, scrollToComment]);

  const handleCommentSubmit = async () => {
    if (!comment.trim()) return;

    try {
      const response = await axios.post(`/api/projects/${projectId}/comments`, {
        text: comment
      });

      setProject(prev => ({
        ...prev,
        comments: [...(prev.comments || []), response.data]
      }));
      setComment('');
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  // Edit mode functions
  const enterEditMode = () => {
    setEditedDescription(project.description);
    setEditMode(true);
  };

  const cancelEdit = () => {
    setEditedDescription(project.description);
    setEditMode(false);
  };

  const handleSaveChanges = async () => {
    try {
      const response = await axios.put(`/api/projects/${projectId}`, {
        name: project.name,
        description: editedDescription
      });
      setProject(response.data);
      setEditMode(false);
    } catch (error) {
      console.error('Error updating project:', error);
      setError(error.response?.data?.message || 'Failed to update project');
    }
  };

  // Menu handler functions
  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  useEffect(() => {
    if (requirements.length > 0) {
      console.log('Requirements updated:', requirements);

      // First, separate features and non-features
      const features = requirements.filter(req => req.type === 'feature');
      const nonFeatures = requirements.filter(req => req.type !== 'feature');

      console.log('Features:', features);
      console.log('Non-features:', nonFeatures);

      // Create a map of feature IDs to their child requirements
      const featureChildren = {};
      nonFeatures.forEach(req => {
        const parentId = req.feature || req.parent;
        if (parentId && parentId !== projectId) {
          if (!featureChildren[parentId]) {
            featureChildren[parentId] = [];
          }
          featureChildren[parentId].push(req);
        }
      });

      console.log('Feature children map:', featureChildren);

      // Get top-level requirements
      const topLevelRequirements = nonFeatures.filter(req =>
        !req.feature ||
        req.feature === projectId ||
        (!req.parent && req.feature === projectId)
      );
      console.log('Top-level requirements:', topLevelRequirements);
    }
  }, [requirements, projectId]);

  const handleCreateProject = async () => {
    console.log('handleCreateProject called');
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const headers = {
        'Authorization': `Bearer ${token}`
      };

      console.log('Creating project with data:', newProject);
      const res = await axios.post('/api/projects', {
        name: newProject.name,
        description: newProject.description,
        addSelf: newProject.addSelf
      }, { headers });
      console.log('Project creation response:', res.data);

      if (!res.data || !res.data._id) {
        throw new Error('Invalid project creation response');
      }

      // Set the project data directly from the response
      setProject(res.data);
      setNewProjectDialogOpen(false);
      setNewProject({
        name: '',
        description: '',
        addSelf: true
      });

      // Trigger navigation update when new project is created
      console.log('Dispatching projectCreated event with user:', {
        userId: user.id || user._id,
        userObject: user,
        projectId: res.data._id
      });
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'projectCreated',
          projectId: res.data._id,
          userId: user.id || user._id // Current user who created the project
        }
      }));

      // Navigate to the new project
      navigate(`/project/${res.data._id}`, { replace: true });
    } catch (error) {
      console.error('Error creating project:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      if (error.response?.status === 401) {
        navigate('/login');
      } else {
        setError(error.response?.data?.message || error.message || 'Failed to create project');
      }
    }
  };

  const handleCreateElement = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const headers = {
        'Authorization': `Bearer ${token}`
      };

      let response;
      if (newElement.type === 'feature') {
        // Create a feature using the features endpoint
        response = await axios.post('/api/features', {
          project: projectId,
          title: newElement.title,
          description: newElement.description
        }, { headers });

        setFeatures([...features, response.data]);
        setNewElementDialogOpen(false);
        setNewElement({
          type: 'feature',
          title: '',
          description: ''
        });

        // Trigger navigation update when feature is created
        window.dispatchEvent(new CustomEvent('updateNavigation', {
          detail: {
            type: 'featureCreated',
            projectId: projectId,
            featureId: response.data._id
          }
        }));

        // For features, we'll stay on the project view and refresh
        const [projectRes, featuresRes, requirementsRes] = await Promise.all([
          axios.get(`/api/projects/${projectId}`, { headers }),
          axios.get(`/api/features/project/${projectId}`, { headers }),
          axios.get(`/api/requirements/project/${projectId}`, { headers })
        ]);
        setProject(projectRes.data);
        setFeatures(featuresRes.data || []);
        setRequirements(requirementsRes.data || []);
      } else {
        // Create a requirement or user story using the requirements endpoint
        response = await axios.post('/api/requirements', {
          project: projectId,
          type: newElement.type,
          title: newElement.title,
          description: newElement.description
        }, { headers });

        setRequirements([...requirements, response.data]);
        setNewElementDialogOpen(false);
        setNewElement({
          type: 'feature',
          title: '',
          description: ''
        });

        // Trigger navigation update when requirement is created
        window.dispatchEvent(new CustomEvent('updateNavigation', {
          detail: {
            type: 'requirementCreated',
            projectId: projectId,
            requirementId: response.data._id
          }
        }));

        // For requirements, navigate to the requirement view
        navigate(`/requirement/${response.data._id}`);
      }
    } catch (error) {
      console.error('Error creating element:', error);
      if (error.response?.status === 401) {
        navigate('/login');
      } else {
        setError(error.response?.data?.message || error.message || 'Failed to create element');
      }
    }
  };

  const handleToggleFeature = (featureId) => {
    setExpandedFeatures(prev => ({
      ...prev,
      [featureId]: !prev[featureId]
    }));
  };

  const getStateColor = (state) => {
    // Use shared state colors for consistency
    return stateColors[state] || 'default';
  };

  const getRequirementIcon = (type) => {
    switch (type) {
      case 'feature':
        return <FolderIcon />;
      case 'user_story':
        return <AssignmentIcon />;
      default:
        return <AssignmentIcon />;
    }
  };

  const renderedRequirements = useMemo(() => {
    // Create a map of feature IDs to their child requirements
    const featureChildren = {};
    requirements.forEach(req => {
      if (req.feature && req.feature !== projectId) {
        if (!featureChildren[req.feature]) {
          featureChildren[req.feature] = [];
        }
        featureChildren[req.feature].push(req);
      }
    });

    // Get top-level requirements
    const topLevelRequirements = requirements.filter(req =>
      !req.feature || req.feature === projectId
    );

    // Render features and their children
    const featureElements = features.map((feature) => {
      const currentVersion = feature.versions[feature.versions.length - 1];
      const childRequirements = featureChildren[feature._id] || [];

      return (
        <React.Fragment key={feature._id}>
          <ListItem
            button
            onClick={(e) => {
              e.stopPropagation();
              handleToggleFeature(feature._id);
            }}
          >
            <ListItemIcon>
              {expandedFeatures[feature._id] ? <ExpandMoreIcon /> : <ChevronRightIcon />}
            </ListItemIcon>
            <ListItemText
              primary={
                <Box
                  sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    // Navigate to feature view instead of requirement view
                    navigate(`/feature/${feature._id}`);
                  }}
                >
                  <FolderIcon sx={{ mr: 1 }} />
                  {currentVersion.title}
                  {childRequirements.length > 0 && (
                    <Chip
                      label={`${childRequirements.length} ${childRequirements.length === 1 ? 'item' : 'items'}`}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  )}
                </Box>
              }
              secondary={
                <RichTextDisplay
                  content={currentVersion.description}
                  inline={true}
                  sx={{
                    fontSize: '0.875rem',
                    color: 'text.secondary'
                  }}
                />
              }
            />
            <Chip
              label={currentVersion.state}
              color={getStateColor(currentVersion.state)}
              size="small"
              sx={{ ml: 1 }}
            />
          </ListItem>
          <Collapse in={expandedFeatures[feature._id]} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {childRequirements.map(child => {
                const childVersion = child.versions[child.versions.length - 1];
                return (
                  <ListItem
                    key={child._id}
                    button
                    onClick={() => navigate(`/requirement/${child._id}`)}
                    sx={{
                      pl: 12,
                      '& .MuiListItemIcon-root': {
                        minWidth: '40px',
                        marginRight: '16px'
                      }
                    }}
                  >
                    <ListItemIcon>
                      {getRequirementIcon(child.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={childVersion.title}
                      secondary={
                        <RichTextDisplay
                          content={childVersion.description}
                          inline={true}
                          sx={{
                            fontSize: '0.875rem',
                            color: 'text.secondary'
                          }}
                        />
                      }
                    />
                    <Chip
                      label={childVersion.state}
                      color={getStateColor(childVersion.state)}
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  </ListItem>
                );
              })}
            </List>
          </Collapse>
        </React.Fragment>
      );
    });

    // Render top-level requirements
    const topLevelElements = topLevelRequirements.map(req => {
      const currentVersion = req.versions[req.versions.length - 1];
      return (
        <ListItem
          key={req._id}
          button
          onClick={() => navigate(`/requirement/${req._id}`)}
        >
          <ListItemIcon>
            {getRequirementIcon(req.type)}
          </ListItemIcon>
          <ListItemText
            primary={currentVersion.title}
            secondary={
              <RichTextDisplay
                content={currentVersion.description}
                inline={true}
                sx={{
                  fontSize: '0.875rem',
                  color: 'text.secondary'
                }}
              />
            }
          />
          <Chip
            label={currentVersion.state}
            color={getStateColor(currentVersion.state)}
            size="small"
            sx={{ ml: 1 }}
          />
        </ListItem>
      );
    });

    // Combine features and top-level requirements
    return [...featureElements, ...topLevelElements];
  }, [features, requirements, expandedFeatures, projectId, navigate]);

  const handleAddUser = async (userId, roles = []) => {
    try {
      const response = await axios.post(`/api/projects/${projectId}/members`, {
        userId,
        roles
      });
      setProject(response.data);

      // Trigger navigation update when user is added to project
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'userAdded',
          projectId: projectId,
          userId: userId
        }
      }));
    } catch (err) {
      console.error('Error adding user:', err);
      setError(err.response?.data?.message || 'Failed to add user to project');
    }
  };

  const handleRemoveUser = async (userId) => {
    try {
      const response = await axios.delete(`/api/projects/${projectId}/members/${userId}`);
      setProject(response.data);

      // Trigger navigation update when user is removed from project
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'userRemoved',
          projectId: projectId,
          userId: userId
        }
      }));
    } catch (err) {
      console.error('Error removing user:', err);
      // You might want to show an error message to the user here
    }
  };

  if (projectId === 'new') {
    return (
      <Dialog
        open={newProjectDialogOpen}
        onClose={() => {
          setNewProjectDialogOpen(false);
          navigate('/');
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Create New Project</DialogTitle>
        <DialogContent>
          <TextField
            label="Project Name"
            value={newProject.name}
            onChange={(e) => setNewProject({ ...newProject, name: e.target.value })}
            fullWidth
            margin="normal"
            required
          />
          <TextField
            label="Description"
            value={newProject.description}
            onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
            fullWidth
            multiline
            rows={4}
            margin="normal"
            required
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={newProject.addSelf}
                onChange={(e) => setNewProject({ ...newProject, addSelf: e.target.checked })}
              />
            }
            label="Add self to Project"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setNewProjectDialogOpen(false);
            navigate('/');
          }}>Cancel</Button>
          <Button
            onClick={handleCreateProject}
            variant="contained"
            disabled={!newProject.name || !newProject.description}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error" gutterBottom>Error: {error}</Typography>
        <Button
          onClick={() => navigate('/')}
          variant="contained"
          sx={{ mt: 2 }}
        >
          Back to Projects
        </Button>
      </Box>
    );
  }

  if (!project) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography gutterBottom>Project not found</Typography>
        <Button
          onClick={() => navigate('/')}
          variant="contained"
          sx={{ mt: 2 }}
        >
          Back to Projects
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Breadcrumbs
        items={[
          {
            label: 'Projects',
            path: '/',
            icon: FolderIcon
          },
          {
            label: project.name,
            path: `/project/${projectId}`,
            icon: FolderIcon
          }
        ]}
      />
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          <FolderIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          {project.name}
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<HistoryIcon />}
            onClick={() => navigate(`/project/${projectId}/documents`)}
          >
            Document Generator
          </Button>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => {
              enterEditMode();
              handleMenuClose();
            }}>
              <EditIcon sx={{ mr: 1 }} />
              Edit
            </MenuItem>
            <MenuItem onClick={() => {
              setNewElement({
                type: 'feature',
                title: '',
                description: ''
              });
              setNewElementDialogOpen(true);
              handleMenuClose();
            }}>
              <AddIcon sx={{ mr: 1 }} />
              Add New Element
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      {/* Description and Comments Split View */}
      <Paper sx={{ mb: 3, overflow: 'hidden' }}>
        <Box sx={{ display: 'flex', gap: 3, height: '400px' }}>
          {/* Description Section */}
          <Box sx={{
            flex: `0 0 ${100 - commentsWidth}%`,
            p: 2,
            borderRight: '1px solid #e0e0e0',
            overflow: 'auto'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Description</Typography>
              {editMode && (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveChanges}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={cancelEdit}
                  >
                    Cancel
                  </Button>
                </Box>
              )}
            </Box>

            {editMode ? (
              <Box sx={{ mt: 2 }}>
                <RichTextEditor
                  value={editedDescription}
                  onChange={setEditedDescription}
                  placeholder="Enter project description..."
                  height={250}
                  disabled={false}
                />
              </Box>
            ) : (
              <RichTextDisplayWithLinking
                content={project.description}
                comments={project.comments || []}
                linkedTexts={linkedTexts}
                onCreateLink={handleCreateLink}
                onLinkHover={handleLinkHover}
                onCreateNewComment={handleCreateNewComment}
                hoveredLinkId={hoveredLinkId}
              />
            )}
          </Box>

          {/* Drag Handle */}
          <Box
            sx={{
              width: '4px',
              backgroundColor: '#e0e0e0',
              cursor: 'col-resize',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              '&:hover': {
                backgroundColor: '#bdbdbd'
              }
            }}
          >
            <DragIcon sx={{ fontSize: 16, color: '#9e9e9e' }} />
          </Box>

          {/* Comments Section */}
          <Box sx={{
            flex: `0 0 ${commentsWidth}%`,
            p: 2,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                  <ChatIcon sx={{ mr: 1, color: '#1976d2' }} />
                  Comments
                </Typography>
                <Badge badgeContent={project.comments?.length || 0} color="primary" />
              </Box>
            </Box>

            {/* Comments List */}
            <Box
              ref={commentsContainerRef}
              sx={{
                flex: 1,
                overflow: 'auto',
                mb: 2,
                '& .comment-item': {
                  transition: 'all 0.3s ease',
                  '&.scrolling-to': {
                    backgroundColor: '#fff3e0',
                    transform: 'scale(1.02)',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                  },
                  '&.hovered': {
                    backgroundColor: '#e3f2fd',
                    transform: 'scale(1.01)'
                  }
                }
              }}
            >
              {project.comments && project.comments.length > 0 ? (
                project.comments.map((comment) => {
                  const hasLinkedText = linkedTexts.some(link => link.commentId === comment._id);
                  const isHovered = hoveredLinkId && linkedTexts.some(link => link.commentId === comment._id && link.id === hoveredLinkId);

                  return (
                    <Box
                      key={comment._id}
                      data-comment-id={comment._id}
                      className={`comment-item ${scrollingToComment === comment._id ? 'scrolling-to' : ''}`}
                      sx={{
                        p: 2,
                        mb: 1,
                        border: '1px solid #e0e0e0',
                        borderRadius: 1,
                        backgroundColor: '#fafafa',
                        transition: 'all 0.3s ease',
                        ...(hasLinkedText && {
                          border: `2px solid ${comment.user?.color || '#1976d2'}40`,
                          backgroundColor: `${comment.user?.color || '#1976d2'}08`,
                        }),
                        ...(isHovered && {
                          backgroundColor: `${comment.user?.color || '#1976d2'}20`,
                          transform: 'scale(1.02)',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                          border: `2px solid ${comment.user?.color || '#1976d2'}80`,
                        }),
                      }}
                    >
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                      <ListItemAvatar sx={{ minWidth: 40 }}>
                        <EnhancedAvatar user={comment.user} size={32} />
                      </ListItemAvatar>
                      <Box sx={{ flex: 1, ml: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                          {comment.user.username}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(comment.createdAt).toLocaleString()}
                        </Typography>
                      </Box>
                    </Box>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {comment.text}
                    </Typography>
                  </Box>
                  );
                })
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No comments yet. Add a comment below or select text in the description to create a linked comment.
                </Typography>
              )}
            </Box>

            {/* Add Comment */}
            <Box sx={{ mt: 'auto' }}>
              <TextField
                fullWidth
                multiline
                rows={3}
                placeholder="Add a comment..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                variant="outlined"
                size="small"
              />
              <Button
                variant="contained"
                onClick={handleCommentSubmit}
                disabled={!comment.trim()}
                sx={{ mt: 1, float: 'right' }}
              >
                Add Comment
              </Button>
            </Box>
          </Box>
        </Box>
      </Paper>

      <Paper sx={{ p: 2, mb: 3 }}>
        {renderedRequirements.length > 0 ? (
          <List>
            {renderedRequirements}
          </List>
        ) : (
          <Box sx={{
            textAlign: 'center',
            py: 6,
            color: 'text.secondary'
          }}>
            <Typography variant="h6" gutterBottom>
              No features or requirements yet
            </Typography>
            <Typography variant="body2" sx={{ mb: 2 }}>
              This project doesn't have any features or requirements.
              Get started by creating your first element.
            </Typography>
            <Typography variant="body2" color="primary">
              Click the "New Element" button above to add a feature, requirement, or user story.
            </Typography>
          </Box>
        )}
      </Paper>

      <Dialog
        open={newElementDialogOpen}
        onClose={() => setNewElementDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Create New Element</DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="normal">
            <InputLabel>Type</InputLabel>
            <Select
              value={newElement.type}
              onChange={(e) => setNewElement({ ...newElement, type: e.target.value })}
              label="Type"
            >
              <MenuItem value="feature">Feature</MenuItem>
              <MenuItem value="requirement">Requirement</MenuItem>
              <MenuItem value="user_story">User Story</MenuItem>
            </Select>
          </FormControl>
          <TextField
            label="Title"
            value={newElement.title}
            onChange={(e) => setNewElement({ ...newElement, title: e.target.value })}
            fullWidth
            margin="normal"
            required
          />
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>Description *</Typography>
            <RichTextEditor
              value={newElement.description}
              onChange={(value) => setNewElement({ ...newElement, description: value })}
              placeholder="Enter description..."
              height={150}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewElementDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleCreateElement}
            variant="contained"
            disabled={!newElement.title || !newElement.description}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* User Management Section */}
      <Paper sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">User Management</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setAddUserDialogOpen(true)}
          >
            Add User
          </Button>
        </Box>
        <Divider sx={{ mb: 2 }} />
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {project.members.map((member) => {
            return (
              <Chip
                key={member.user._id}
                label={member.user.username}
                avatar={<EnhancedAvatar user={member.user} size={24} />}
                sx={{ bgcolor: member.user.color + '20', m: 0.5 }}
                onDelete={() => handleRemoveUser(member.user._id)}
              />
            );
          })}
        </Box>
      </Paper>

      <AddUserDialog
        open={addUserDialogOpen}
        onClose={() => setAddUserDialogOpen(false)}
        onAdd={handleAddUser}
        existingUsers={project.members}
        title="Add User to Project"
      />
    </Box>
  );
};

export default ProjectView;