import React, { useState, useRef, useCallback, useEffect } from 'react';
import { 
  Box, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  TextField, 
  Typography, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemAvatar, 
  Avatar, 
  Paper, 
  Chip,
  IconButton
} from '@mui/material';
import { 
  Add as AddIcon, 
  Link as LinkIcon, 
  Close as CloseIcon 
} from '@mui/icons-material';

const RichTextDisplayWithLinking = ({
  content,
  sx = {},
  inline = false,
  comments = [],
  linkedTexts = [],
  onCreateLink,
  onCreateNewComment,
  onLinkHover,
  hoveredLinkId = null,
  scrollToComment
}) => {
  const [selectedText, setSelectedText] = useState('');
  const [selectionRange, setSelectionRange] = useState(null);
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [newCommentText, setNewCommentText] = useState('');
  const contentRef = useRef(null);

  // Handle text selection
  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0 && contentRef.current) {
      const range = selection.getRangeAt(0);
      const selectedText = selection.toString().trim();
      
      // Check if selection is within our content area
      if (selectedText && contentRef.current.contains(range.commonAncestorContainer)) {
        setSelectedText(selectedText);
        setSelectionRange({
          startOffset: range.startOffset,
          endOffset: range.endOffset,
          startContainer: range.startContainer,
          endContainer: range.endContainer
        });
        setShowLinkDialog(true);
      }
    }
  }, []);

  // Process content to add highlighting for linked texts
  const processContentWithHighlights = useCallback((htmlContent) => {
    if (!linkedTexts.length) return htmlContent;

    let processedContent = htmlContent;
    
    linkedTexts.forEach((link) => {
      const linkId = link.id;
      const comment = comments.find(c => c._id === link.commentId || c.id === link.commentId);
      const highlightColor = comment?.user?.color || link.highlightColor || '#1976d2';
      
      // Create a regex to find the exact text
      const textRegex = new RegExp(`(${link.text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
      
      const isHovered = hoveredLinkId === linkId;
      const hoverStyles = isHovered ? 
        'transform: scale(1.02); box-shadow: 0 2px 8px rgba(0,0,0,0.15);' : '';
      
      const replacement = `<span 
        data-link-id="${linkId}" 
        style="
          background-color: ${highlightColor}20; 
          border-bottom: 2px solid ${highlightColor}40;
          padding: 1px 2px;
          border-radius: 2px;
          cursor: pointer;
          transition: all 0.2s ease;
          ${hoverStyles}
        "
        title="Linked to comment"
      >$1</span>`;
      
      processedContent = processedContent.replace(textRegex, replacement);
    });

    return processedContent;
  }, [linkedTexts, comments, hoveredLinkId]);

  // Handle linking to existing comment
  const handleCreateLink = useCallback((commentId) => {
    if (!selectedText || !selectionRange) return;

    const newLink = {
      id: `link-${Date.now()}`,
      text: selectedText,
      commentId: commentId,
      startOffset: selectionRange.startOffset,
      endOffset: selectionRange.endOffset,
      highlightColor: comments.find(c => c._id === commentId || c.id === commentId)?.user?.color || '#1976d2'
    };

    onCreateLink?.(newLink);
    setShowLinkDialog(false);
    setSelectedText('');
    setSelectionRange(null);
    setNewCommentText('');
    
    // Clear selection
    window.getSelection().removeAllRanges();
  }, [selectedText, selectionRange, comments, onCreateLink]);

  // Handle creating new comment and linking
  const handleCreateNewComment = useCallback(() => {
    if (!selectedText || !selectionRange || !newCommentText.trim()) return;

    onCreateNewComment?.(selectedText, selectionRange, newCommentText);
    setShowLinkDialog(false);
    setSelectedText('');
    setSelectionRange(null);
    setNewCommentText('');
    
    // Clear selection
    window.getSelection().removeAllRanges();
  }, [selectedText, selectionRange, newCommentText, onCreateNewComment]);

  // Handle hover events for linked text
  const handleMouseOver = useCallback((e) => {
    const linkElement = e.target.closest('[data-link-id]');
    if (linkElement) {
      const linkId = linkElement.getAttribute('data-link-id');
      onLinkHover?.(linkId, true);
    }
  }, [onLinkHover]);

  const handleMouseOut = useCallback((e) => {
    const linkElement = e.relatedTarget?.closest('[data-link-id]');
    if (!linkElement) {
      onLinkHover?.(null, false);
    }
  }, [onLinkHover]);

  if (!content) {
    return null;
  }

  const processedContent = processContentWithHighlights(content);

  // For inline usage
  if (inline) {
    return (
      <span
        ref={contentRef}
        style={{
          fontSize: 'inherit',
          color: 'inherit',
          ...sx,
        }}
        dangerouslySetInnerHTML={{ __html: processedContent }}
        onMouseUp={handleTextSelection}
        onMouseOver={handleMouseOver}
        onMouseOut={handleMouseOut}
      />
    );
  }

  // For block usage
  return (
    <>
      <Box
        ref={contentRef}
        sx={{
          '& img': {
            maxWidth: '100%',
            height: 'auto',
          },
          '& p': {
            margin: '0 0 1em 0',
            '&:last-child': {
              marginBottom: 0,
            },
          },
          '& ul, & ol': {
            paddingLeft: '1.5em',
            margin: '0 0 1em 0',
          },
          '& li': {
            marginBottom: '0.25em',
          },
          '& h1, & h2, & h3, & h4, & h5, & h6': {
            margin: '1em 0 0.5em 0',
            '&:first-child': {
              marginTop: 0,
            },
          },
          '& blockquote': {
            borderLeft: '4px solid #e0e0e0',
            paddingLeft: '1em',
            margin: '1em 0',
            fontStyle: 'italic',
          },
          '& code': {
            backgroundColor: '#f5f5f5',
            padding: '0.2em 0.4em',
            borderRadius: '3px',
            fontFamily: 'monospace',
          },
          '& pre': {
            backgroundColor: '#f5f5f5',
            padding: '1em',
            borderRadius: '4px',
            overflow: 'auto',
            '& code': {
              backgroundColor: 'transparent',
              padding: 0,
            },
          },
          '& table': {
            borderCollapse: 'collapse',
            width: '100%',
            margin: '1em 0',
          },
          '& th, & td': {
            border: '1px solid #e0e0e0',
            padding: '0.5em',
            textAlign: 'left',
          },
          '& th': {
            backgroundColor: '#f5f5f5',
            fontWeight: 'bold',
          },
          // Hover effects for linked text
          '& [data-link-id]:hover': {
            backgroundColor: 'rgba(25, 118, 210, 0.1) !important',
            transform: 'scale(1.02)',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          },
          ...sx,
        }}
        dangerouslySetInnerHTML={{ __html: processedContent }}
        onMouseUp={handleTextSelection}
        onMouseOver={handleMouseOver}
        onMouseOut={handleMouseOut}
      />

      {/* Link Dialog */}
      <Dialog 
        open={showLinkDialog} 
        onClose={() => setShowLinkDialog(false)}
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LinkIcon color="primary" />
            Link Text to Comment
          </Box>
          <IconButton onClick={() => setShowLinkDialog(false)}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            Selected text: <strong>"{selectedText}"</strong>
          </Typography>

          {/* Create New Comment Section */}
          <Paper sx={{ p: 2, mb: 3, bgcolor: 'primary.50' }}>
            <Typography variant="subtitle2" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <AddIcon color="primary" />
              Create New Comment
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={3}
              placeholder="Write your comment about the selected text..."
              value={newCommentText}
              onChange={(e) => setNewCommentText(e.target.value)}
              sx={{ mb: 2 }}
            />
            <Button
              variant="contained"
              onClick={handleCreateNewComment}
              disabled={!newCommentText.trim()}
              startIcon={<AddIcon />}
            >
              Create Comment & Link
            </Button>
          </Paper>

          {/* Existing Comments Section */}
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            Or link to an existing comment:
          </Typography>
          <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
            {comments.length > 0 ? (
              <List>
                {comments.map((comment) => (
                  <ListItem
                    key={comment._id || comment.id}
                    button
                    onClick={() => handleCreateLink(comment._id || comment.id)}
                    sx={{
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                      '&:hover': {
                        backgroundColor: 'action.hover',
                      },
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar 
                        sx={{ 
                          bgcolor: comment.user?.color || comment.color || '#1976d2',
                          width: 32,
                          height: 32,
                          fontSize: '0.875rem'
                        }}
                      >
                        {comment.user?.username?.charAt(0).toUpperCase() || 
                         comment.author?.charAt(0).toUpperCase() || 'U'}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={comment.text}
                      secondary={`${comment.user?.username || comment.author || 'Unknown'} • ${
                        comment.createdAt ? new Date(comment.createdAt).toLocaleDateString() : comment.time || 'Unknown date'
                      }`}
                      primaryTypographyProps={{
                        sx: { 
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                        }
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 2 }}>
                No comments available. Create a new comment above.
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowLinkDialog(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default RichTextDisplayWithLinking;
